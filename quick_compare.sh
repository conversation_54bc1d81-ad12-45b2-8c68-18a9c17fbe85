#!/bin/bash

# 快速数据库对比脚本
# 使用方法: ./quick_compare.sh

echo "=== 卡商平台数据库变化对比工具 ==="
echo ""

# 检查Python和依赖
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 python3，请先安装 Python 3"
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "错误: 未找到 requirements.txt 文件"
    exit 1
fi

# 安装依赖（如果需要）
echo "检查并安装依赖..."
pip3 install -r requirements.txt > /dev/null 2>&1

echo "1. 捕获操作前快照..."
python3 db_comparison.py before

if [ $? -ne 0 ]; then
    echo "错误: 捕获操作前快照失败"
    exit 1
fi

echo ""
echo "✓ 操作前快照已保存"
echo ""
echo "现在请执行你的卡商操作（出卡商卖卡、收卡商收卡等）"
echo "操作完成后按任意键继续..."
read -n 1 -s

echo ""
echo "2. 捕获操作后快照..."
python3 db_comparison.py after

if [ $? -ne 0 ]; then
    echo "错误: 捕获操作后快照失败"
    exit 1
fi

echo ""
echo "3. 比较数据变化..."
python3 db_comparison.py compare

echo ""
echo "✓ 对比完成！详细结果已保存到比较文件中。"

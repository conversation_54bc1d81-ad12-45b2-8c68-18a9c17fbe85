# 数据库快照文件
db_snapshot_*.json
*_snapshot_*.json

# 比较结果文件
db_comparison_*.json
*_comparison_*.json

# 示例文件
example_*.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 日志文件
*.log

# 临时文件
*.tmp
*.temp

# 配置文件备份
config.json.bak
config_*.json

#!/usr/bin/env python3
"""
数据库表变化对比脚本
用于对比卡商平台操作前后数据表的变化
"""

import pymysql
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Tuple
import argparse
import os

class DatabaseComparator:
    def __init__(self, host: str, port: int, username: str, password: str, database: str, tables_to_monitor: List[str] = None):
        """初始化数据库连接"""
        self.connection_config = {
            'host': host,
            'port': port,
            'user': username,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

        # 需要监控的表
        self.tables_to_monitor = tables_to_monitor or [
            'orders',
            'order_statistics',
            'order_card_logs',
            'admin_operation_log',
            'transaction_records',
            'transactions',
            'order_cards',
            'order_card_logs',
            'company_card_profiles',
            'platform_transaction_records',
            'platform_transactions',
            'order_card_queues'
        ]
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.connection_config)
    
    def get_table_data(self, table_name: str) -> List[Dict]:
        """获取表的所有数据"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT * FROM {table_name}")
                return cursor.fetchall()
        finally:
            connection.close()
    
    def get_table_count(self, table_name: str) -> int:
        """获取表的记录数"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                result = cursor.fetchone()
                return result['count']
        finally:
            connection.close()
    
    def create_data_hash(self, data: List[Dict]) -> str:
        """为数据创建哈希值，用于快速比较"""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def capture_snapshot(self) -> Dict[str, Any]:
        """捕获当前数据库状态快照"""
        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'tables': {}
        }
        
        print("正在捕获数据库快照...")
        for table in self.tables_to_monitor:
            try:
                print(f"  - 处理表: {table}")
                data = self.get_table_data(table)
                count = len(data)
                data_hash = self.create_data_hash(data)
                
                snapshot['tables'][table] = {
                    'count': count,
                    'hash': data_hash,
                    'data': data
                }
                print(f"    记录数: {count}")
            except Exception as e:
                print(f"    错误: {e}")
                snapshot['tables'][table] = {
                    'error': str(e)
                }
        
        return snapshot
    
    def save_snapshot(self, snapshot: Dict[str, Any], filename: str):
        """保存快照到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(snapshot, f, ensure_ascii=False, indent=2, default=str)
        print(f"快照已保存到: {filename}")
    
    def load_snapshot(self, filename: str) -> Dict[str, Any]:
        """从文件加载快照"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def compare_snapshots(self, before_file: str, after_file: str) -> Dict[str, Any]:
        """比较两个快照"""
        before = self.load_snapshot(before_file)
        after = self.load_snapshot(after_file)
        
        comparison = {
            'before_timestamp': before['timestamp'],
            'after_timestamp': after['timestamp'],
            'changes': {}
        }
        
        print("\n=== 数据库变化对比报告 ===")
        print(f"操作前时间: {before['timestamp']}")
        print(f"操作后时间: {after['timestamp']}")
        print()
        
        for table in self.tables_to_monitor:
            print(f"表: {table}")
            
            if table not in before['tables'] or table not in after['tables']:
                print("  - 表数据缺失，跳过比较")
                continue
            
            before_table = before['tables'][table]
            after_table = after['tables'][table]
            
            # 检查是否有错误
            if 'error' in before_table or 'error' in after_table:
                print("  - 表数据读取错误，跳过比较")
                continue
            
            before_count = before_table['count']
            after_count = after_table['count']
            count_diff = after_count - before_count
            
            print(f"  记录数变化: {before_count} -> {after_count} ({count_diff:+d})")
            
            # 比较数据哈希
            if before_table['hash'] != after_table['hash']:
                print("  ✓ 数据内容发生变化")
                
                # 详细分析变化
                changes = self.analyze_detailed_changes(
                    before_table['data'], 
                    after_table['data']
                )
                comparison['changes'][table] = changes
                
                if changes['new_records']:
                    print(f"    新增记录: {len(changes['new_records'])} 条")
                if changes['deleted_records']:
                    print(f"    删除记录: {len(changes['deleted_records'])} 条")
                if changes['modified_records']:
                    print(f"    修改记录: {len(changes['modified_records'])} 条")
            else:
                print("  - 数据内容无变化")
            
            print()
        
        return comparison

    def analyze_detailed_changes(self, before_data: List[Dict], after_data: List[Dict]) -> Dict[str, List]:
        """详细分析数据变化"""
        # 假设每个表都有id字段作为主键
        before_dict = {}
        after_dict = {}

        # 尝试找到主键字段
        primary_key = self.find_primary_key(before_data + after_data)

        # 构建字典以便比较
        for record in before_data:
            if primary_key and primary_key in record:
                before_dict[record[primary_key]] = record

        for record in after_data:
            if primary_key and primary_key in record:
                after_dict[record[primary_key]] = record

        changes = {
            'new_records': [],
            'deleted_records': [],
            'modified_records': []
        }

        # 找出新增记录
        for key, record in after_dict.items():
            if key not in before_dict:
                changes['new_records'].append(record)

        # 找出删除记录
        for key, record in before_dict.items():
            if key not in after_dict:
                changes['deleted_records'].append(record)

        # 找出修改记录
        for key in before_dict:
            if key in after_dict:
                if json.dumps(before_dict[key], sort_keys=True, default=str) != \
                   json.dumps(after_dict[key], sort_keys=True, default=str):
                    changes['modified_records'].append({
                        'before': before_dict[key],
                        'after': after_dict[key]
                    })

        return changes

    def find_primary_key(self, data: List[Dict]) -> str:
        """尝试找到主键字段"""
        if not data:
            return None

        # 常见的主键字段名
        possible_keys = ['id', 'ID', 'pk', 'primary_key']

        for key in possible_keys:
            if key in data[0]:
                return key

        # 如果没有找到，返回第一个字段
        return list(data[0].keys())[0] if data[0] else None


def load_config(config_file: str = 'config.json') -> Dict[str, Any]:
    """加载配置文件"""
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        # 返回默认配置
        return {
            "database": {
                "host": "nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com",
                "port": 3306,
                "username": "root",
                "password": "BwYvW64msQ5Lndu2Umpn",
                "database": "staging_card"
            },
            "tables_to_monitor": [
                "orders", "order_statistics", "order_card_images",
                "admin_operation_log", "transaction_records", "transactions",
                "order_cards", "order_card_logs", "company_card_profiles",
                "platform_transaction_records", "platform_transactions",
                "order_card_queues"
            ],
            "output": {
                "before_snapshot": "db_snapshot_before.json",
                "after_snapshot": "db_snapshot_after.json",
                "comparison_prefix": "db_comparison"
            }
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库表变化对比工具')
    parser.add_argument('action', choices=['before', 'after', 'compare'],
                       help='操作类型: before(操作前快照), after(操作后快照), compare(比较快照)')
    parser.add_argument('--config', default='config.json',
                       help='配置文件路径')
    parser.add_argument('--before-file',
                       help='操作前快照文件名（覆盖配置文件设置）')
    parser.add_argument('--after-file',
                       help='操作后快照文件名（覆盖配置文件设置）')

    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 数据库连接配置
    db_config = config['database']
    tables_to_monitor = config['tables_to_monitor']

    # 文件名配置
    before_file = args.before_file or config['output']['before_snapshot']
    after_file = args.after_file or config['output']['after_snapshot']

    comparator = DatabaseComparator(
        host=db_config['host'],
        port=db_config['port'],
        username=db_config['username'],
        password=db_config['password'],
        database=db_config['database'],
        tables_to_monitor=tables_to_monitor
    )

    try:
        if args.action == 'before':
            print("=== 捕获操作前数据库快照 ===")
            snapshot = comparator.capture_snapshot()
            comparator.save_snapshot(snapshot, before_file)
            print(f"\n操作前快照已保存。现在可以执行你的卡商操作，然后运行:")
            print(f"python {__file__} after")

        elif args.action == 'after':
            print("=== 捕获操作后数据库快照 ===")
            snapshot = comparator.capture_snapshot()
            comparator.save_snapshot(snapshot, after_file)
            print(f"\n操作后快照已保存。现在可以比较变化:")
            print(f"python {__file__} compare")

        elif args.action == 'compare':
            print("=== 比较数据库快照 ===")
            if not os.path.exists(before_file):
                print(f"错误: 找不到操作前快照文件 {before_file}")
                return
            if not os.path.exists(after_file):
                print(f"错误: 找不到操作后快照文件 {after_file}")
                return

            comparison = comparator.compare_snapshots(before_file, after_file)

            # 保存详细比较结果
            comparison_prefix = config['output']['comparison_prefix']
            comparison_file = f"{comparison_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(comparison_file, 'w', encoding='utf-8') as f:
                json.dump(comparison, f, ensure_ascii=False, indent=2, default=str)
            print(f"详细比较结果已保存到: {comparison_file}")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

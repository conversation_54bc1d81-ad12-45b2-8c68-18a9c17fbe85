#!/usr/bin/env python3
"""
使用示例脚本
演示如何使用数据库对比工具
"""

import os
import time
from db_comparison import DatabaseComparator, load_config

def example_usage():
    """使用示例"""
    print("=== 数据库对比工具使用示例 ===\n")
    
    # 加载配置
    config = load_config()
    db_config = config['database']
    
    # 创建对比器
    comparator = DatabaseComparator(
        host=db_config['host'],
        port=db_config['port'],
        username=db_config['username'],
        password=db_config['password'],
        database=db_config['database'],
        tables_to_monitor=config['tables_to_monitor']
    )
    
    try:
        # 1. 捕获操作前快照
        print("1. 捕获操作前快照...")
        before_snapshot = comparator.capture_snapshot()
        before_file = "example_before.json"
        comparator.save_snapshot(before_snapshot, before_file)
        print(f"   快照已保存到: {before_file}")
        
        # 模拟等待用户操作
        print("\n2. 模拟等待用户操作...")
        print("   (在实际使用中，这里是你执行卡商操作的时间)")
        time.sleep(2)  # 模拟操作时间
        
        # 3. 捕获操作后快照
        print("\n3. 捕获操作后快照...")
        after_snapshot = comparator.capture_snapshot()
        after_file = "example_after.json"
        comparator.save_snapshot(after_snapshot, after_file)
        print(f"   快照已保存到: {after_file}")
        
        # 4. 比较变化
        print("\n4. 比较数据变化...")
        comparison = comparator.compare_snapshots(before_file, after_file)
        
        # 5. 保存比较结果
        comparison_file = "example_comparison.json"
        with open(comparison_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(comparison, f, ensure_ascii=False, indent=2, default=str)
        print(f"   比较结果已保存到: {comparison_file}")
        
        # 6. 清理示例文件
        print("\n5. 清理示例文件...")
        for file in [before_file, after_file, comparison_file]:
            if os.path.exists(file):
                os.remove(file)
                print(f"   已删除: {file}")
        
        print("\n✓ 示例完成！")
        print("\n实际使用步骤:")
        print("1. python db_comparison.py before")
        print("2. 执行你的卡商操作")
        print("3. python db_comparison.py after")
        print("4. python db_comparison.py compare")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    example_usage()

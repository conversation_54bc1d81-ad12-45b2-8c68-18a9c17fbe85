#!/usr/bin/env python3
"""
测试数据库连接脚本
"""

import pymysql
import json
from db_comparison import load_config

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    try:
        # 加载配置
        config = load_config()
        db_config = config['database']
        
        print(f"连接到数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 创建连接
        connection = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['username'],
            password=db_config['password'],
            database=db_config['database'],
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✓ 数据库连接成功")
        
        # 测试表访问
        tables_to_monitor = config['tables_to_monitor']
        print(f"\n检查 {len(tables_to_monitor)} 个监控表:")
        
        with connection.cursor() as cursor:
            for table in tables_to_monitor:
                try:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    result = cursor.fetchone()
                    count = result['count']
                    print(f"  ✓ {table}: {count} 条记录")
                except Exception as e:
                    print(f"  ✗ {table}: 错误 - {e}")
        
        connection.close()
        print("\n✓ 所有测试完成")
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_database_connection()

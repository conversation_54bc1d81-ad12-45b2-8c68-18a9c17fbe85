# 数据库表变化对比工具

这个工具用于对比卡商平台操作前后数据库表的变化，帮助分析出卡商卖卡、收卡商收卡等操作对数据库的影响。

## 功能特点

- 支持多表同时监控
- 自动检测记录的新增、删除和修改
- 生成详细的变化报告
- 支持JSON格式的快照保存和加载

## 监控的数据表

- `orders` - 订单表
- `order_statistics` - 订单统计表
- `order_card_images` - 订单卡片图片表
- `admin_operation_log` - 管理员操作日志表
- `transaction_records` - 交易记录表
- `transactions` - 交易表
- `order_cards` - 订单卡片表
- `order_card_logs` - 订单卡片日志表
- `company_card_profiles` - 公司卡片配置表
- `platform_transaction_records` - 平台交易记录表
- `platform_transactions` - 平台交易表
- `order_card_queues` - 订单卡片队列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 捕获操作前快照

在执行卡商操作之前，先捕获数据库当前状态：

```bash
python db_comparison.py before
```

这会创建一个 `db_snapshot_before.json` 文件，包含所有监控表的当前数据。

### 2. 执行你的卡商操作

现在可以执行你的卡商平台操作，比如：
- 出卡商卖卡
- 收卡商收卡
- 其他相关操作

### 3. 捕获操作后快照

操作完成后，捕获数据库的新状态：

```bash
python db_comparison.py after
```

这会创建一个 `db_snapshot_after.json` 文件。

### 4. 比较变化

比较操作前后的数据变化：

```bash
python db_comparison.py compare
```

这会显示详细的变化报告，并生成一个带时间戳的比较结果文件。

## 自定义选项

你也可以指定自定义的快照文件名：

```bash
# 使用自定义文件名
python db_comparison.py before --before-file my_before_snapshot.json
python db_comparison.py after --after-file my_after_snapshot.json
python db_comparison.py compare --before-file my_before_snapshot.json --after-file my_after_snapshot.json
```

## 输出示例

```
=== 数据库变化对比报告 ===
操作前时间: 2024-01-15T10:30:00.123456
操作后时间: 2024-01-15T10:35:00.654321

表: orders
  记录数变化: 150 -> 151 (+1)
  ✓ 数据内容发生变化
    新增记录: 1 条

表: order_cards
  记录数变化: 300 -> 305 (+5)
  ✓ 数据内容发生变化
    新增记录: 5 条

表: transaction_records
  记录数变化: 500 -> 502 (+2)
  ✓ 数据内容发生变化
    新增记录: 2 条

表: admin_operation_log
  记录数变化: 1000 -> 1001 (+1)
  ✓ 数据内容发生变化
    新增记录: 1 条
```

## 注意事项

1. 确保数据库连接信息正确
2. 运行脚本的用户需要有读取所有监控表的权限
3. 对于大表，快照操作可能需要一些时间
4. 快照文件可能会比较大，请确保有足够的磁盘空间

## 数据库配置

脚本中已经预配置了数据库连接信息：
- 主机: nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com
- 端口: 3306
- 数据库: staging_card
- 用户名: root

如需修改，请编辑 `db_comparison.py` 文件中的 `db_config` 部分。

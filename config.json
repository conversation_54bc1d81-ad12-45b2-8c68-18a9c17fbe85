{"database": {"host": "nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com", "port": 3306, "username": "root", "password": "BwYvW64msQ5Lndu2Umpn", "database": "staging_card"}, "tables_to_monitor": ["orders", "order_statistics", "order_card_logs", "admin_operation_log", "transaction_records", "transactions", "order_cards", "order_card_logs", "company_card_profiles", "platform_transaction_records", "platform_transactions", "order_card_queues"], "output": {"before_snapshot": "db_snapshot_before.json", "after_snapshot": "db_snapshot_after.json", "comparison_prefix": "db_comparison"}}